import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import (mean_squared_error, mean_absolute_error,
                            r2_score, explained_variance_score,
                            max_error, mean_absolute_percentage_error)
from sklearn.ensemble import (RandomForestRegressor,
                            GradientBoostingRegressor,
                            ExtraTreesRegressor)
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Set style for all plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 1. Data Preparation (using your actual data)
df = pd.read_csv('Samples_Export_scale_10_23aug2025.csv')
print(df)

# df.drop_duplicates(subset=None, inplace=True)
# print(df)

max(df['B2'])

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# data = pd.read_csv("Samples_Export_Scale15.csv")

# data.drop_duplicates(subset=None, inplace=True)

# data.to_csv("./Samples_NoDuplicate.csv", index=False)

data = df

print(data)
print(data.describe())

# sns.histplot(data["AGB"], kde=True, bins=20)
# plt.xlabel("AGB")
# plt.ylabel("Frequency")
# plt.show()

# print("AGB mean: ", data["AGB"].mean(), "standard deviation: ", data["AGB"].std())

# plt.figure(figsize=(18, 12))

# sns.histplot(data['AGB'], kde=True, bins=30)
# plt.title('AGB Distribution')
# plt.xlabel('AGB')
# plt.ylabel('Frequency')
# plt.show()

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(data[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
plt.show()

# for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
#     plt.subplot(2, 3, i+1)
#     sns.boxplot(data[band])
#     plt.title(f'{band} Distribution')
#     plt.xlabel(band)
# plt.show()

# # B2 normalization
# b2_zscore = (data["B2"] - data["B2"].mean()) / data["B2"].std()
# print(b2_zscore)

# b2_outlier = np.where(np.abs(b2_zscore) > 3)
# print(b2_outlier)

# All outliers
outliers = set()
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    z_score = np.abs((data[band] - data[band].mean()) / data[band].std())
    for j in range(len(data[band])):
        if z_score[j] > 3:
            outliers.add(j)

outliers = list(outliers)

outliers.sort()

print(outliers)

print(len(outliers))

data = data.drop(outliers, axis='index')

print(data)
print(len(data))

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(data[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
plt.show()


df = data

# 2. Data Distribution Plots
plt.figure(figsize=(18, 12))

# AGB Distribution
plt.subplot(2, 3, 1)
sns.histplot(df['AGB'], kde=True, bins=30)
plt.title('AGB Distribution')
plt.xlabel('AGB')
plt.ylabel('Frequency')

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(df[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
#plt.tight_layout()
#plt.savefig('data_distributions.png', dpi=300)
#plt.close()

# 3. Correlation Analysis
plt.figure(figsize=(10, 8))
corr_matrix = df.corr()
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Feature Correlation Matrix')
plt.tight_layout()
#plt.savefig('correlation_matrix.png', dpi=300)
#plt.close()

# 4. Train-Test Split
X = df[[ 'B2','B3','B4','B8','B11', 'B12']]
y = df['AGB']/max(df['AGB'])
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

# 5. Model Training
models = {
    'Random Forest': RandomForestRegressor(n_estimators=200, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
    'XGBoost': XGBRegressor(n_estimators=200, random_state=42),
    'LightGBM': LGBMRegressor(n_estimators=200, random_state=42),
    'SVR': SVR(kernel='rbf', C=1.0, epsilon=0.1),
    'Linear Regression': LinearRegression()
}

# Model Training and Evaluation with Additional Metrics
results = []
predictions = {}

for name, model in models.items():
    # Train model
    model.fit(X_train, y_train)

    # Make predictions
    y_pred = model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    mean_err = np.mean(y_test - y_pred)
    median_err = np.median(y_test - y_pred)
    std_err = np.std(y_test - y_pred)

    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100,  # Convert to percentage
        'Mean Error': mean_err,
        'Median Error': median_err,
        'Std Error': std_err
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)
print("\nModel Performance Comparison:")
print(results_df.to_string(index=False))

# 5. Model Training
models = {
    'Random Forest': RandomForestRegressor(n_estimators=200, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
    'XGBoost': XGBRegressor(n_estimators=200, random_state=42),
    'LightGBM': LGBMRegressor(n_estimators=200, random_state=42),
    'SVR': SVR(kernel='rbf', C=1.0, epsilon=0.1),
    'Linear Regression': LinearRegression()
}

# Model Training and Evaluation with Additional Metrics
results = []
predictions = {}

for name, model in models.items():
    # Train model
    model.fit(X_train, y_train)

    # Make predictions
    y_pred = model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    mean_err = np.mean(y_test - y_pred)
    median_err = np.median(y_test - y_pred)
    std_err = np.std(y_test - y_pred)

    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100,  # Convert to percentage
        'Mean Error': mean_err,
        'Median Error': median_err,
        'Std Error': std_err
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)
print("\nModel Performance Comparison:")
print(results_df.to_string(index=False))

import pandas as pd
import numpy as np
from sklearn.model_selection import GridSearchCV, train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from sklearn.metrics import (mean_squared_error, mean_absolute_error, r2_score,
                             explained_variance_score, max_error, mean_absolute_percentage_error)

# --- Use the actual data from the previous section ---
# X and y are already defined from the previous section
# X = df[[ 'B2','B3','B4','B8','B11', 'B12']]
# y = df['AGB']/max(df['AGB'])
# X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)
# --------------------------------------------------------------------


# 5. Model Training with Hyperparameter Tuning

# Initial model instances
models = {
    'Random Forest': RandomForestRegressor(random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(random_state=42),
    'XGBoost': XGBRegressor(random_state=42),
    'LightGBM': LGBMRegressor(random_state=42),
    'SVR': SVR(),
    'Linear Regression': LinearRegression()
}

# Parameter grids for tuning
param_grids = {
    'Random Forest': {
        'n_estimators': [100, 200],
        'max_depth': [10, 20],
        'min_samples_split': [2, 5]
    },
    'Gradient Boosting': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'max_depth': [3, 5]
    },
    'XGBoost': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'max_depth': [3, 5]
    },
    'LightGBM': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'num_leaves': [31, 50]
    },
    'SVR': {
        'C': [1, 10],
        'kernel': ['rbf']
    },
    'Linear Regression': {} # No parameters to tune here
}


# Model Training, Tuning, and Evaluation
results = []
predictions = {}
best_params_found = {}

for name, model in models.items():
    print(f"--- Tuning and Training {name} ---")

    # If there's a parameter grid for the model, perform GridSearchCV
    if param_grids[name]:
        # Set up GridSearchCV
        # cv=3 means 3-fold cross-validation.
        # scoring='r2' tells GridSearchCV to optimize for the R-squared score.
        grid_search = GridSearchCV(estimator=model, param_grid=param_grids[name], cv=3,
                                   scoring='r2', n_jobs=-1, verbose=1)
        
        # Train with grid search
        grid_search.fit(X_train, y_train)
        
        # Get the best model found by the grid search
        best_model = grid_search.best_estimator_
        best_params_found[name] = grid_search.best_params_
        print(f"Best parameters for {name}: {grid_search.best_params_}")
    else:
        # If no parameters to tune, just fit the model
        best_model = model
        best_model.fit(X_train, y_train)
        best_params_found[name] = "N/A"

    # Make predictions with the best model
    y_pred = best_model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    
    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)

print("\n--- Best Parameters Found ---")
for name, params in best_params_found.items():
    print(f"{name}: {params}")

print("\n--- Model Performance Comparison (After Tuning) ---")
print(results_df.to_string(index=False))

# 6. Model Performance Plots
plt.figure(figsize=(15, 10))

# Model Comparison Bar Plot
plt.subplot(2, 2, 1)
sns.barplot(x='Model', y='R2', data=results_df)
plt.title('Model Comparison (R² Score)')
plt.xticks(rotation=45)
plt.ylim(0, 1)

plt.subplot(2, 2, 2)
sns.barplot(x='Model', y='RMSE', data=results_df)
plt.title('Model Comparison (RMSE)')
plt.xticks(rotation=45)

plt.subplot(2, 2, 3)
sns.barplot(x='Model', y='MAE', data=results_df)
plt.title('Model Comparison (MAE)')
plt.xticks(rotation=45)

# Actual vs Predicted Scatter Plots
plt.subplot(2, 2, 4)
for name, y_pred in predictions.items():
    plt.scatter(y_test, y_pred, alpha=0.5, label=name)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--')
plt.xlabel('Actual AGB')
plt.ylabel('Predicted AGB')
plt.title('Actual vs Predicted AGB')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

plt.tight_layout()
#plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# 7. Residual Analysis Plots
plt.figure(figsize=(15, 10))
for i, (name, y_pred) in enumerate(predictions.items(), 1):
    residuals = y_test - y_pred
    plt.subplot(2, 3, i)
    sns.scatterplot(x=y_pred, y=residuals, alpha=0.5)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(f'Residual Plot: {name}')
plt.tight_layout()
#plt.savefig('residual_analysis.png', dpi=300)
plt.show()

# 8. Feature Importance Plots
plt.figure(figsize=(15, 10))

# Define feature names - use the original feature names from the dataset
feature_names = ['B2', 'B3', 'B4', 'B8', 'B11', 'B12']

for i, (name, model) in enumerate(models.items(), 1):
    if hasattr(model, 'feature_importances_'):
        plt.subplot(2, 3, i)
        importances = pd.Series(model.feature_importances_, index=feature_names)
        importances.sort_values().plot(kind='barh')
        plt.title(f'Feature Importance: {name}')
        plt.xlabel('Importance')
    elif hasattr(model, 'coef_'):
        plt.subplot(2, 3, i)
        coef = pd.Series(model.coef_, index=feature_names)
        coef.sort_values().plot(kind='barh')
        plt.title(f'Feature Coefficients: {name}')
        plt.xlabel('Coefficient Value')
plt.tight_layout()
#plt.savefig('feature_importance.png', dpi=300)
plt.show()

# 9. Prediction Error Distribution
plt.figure(figsize=(15, 10))
for i, (name, y_pred) in enumerate(predictions.items(), 1):
    plt.subplot(2, 3, i)
    errors = y_test - y_pred
    sns.histplot(errors, kde=True, bins=30)
    plt.xlabel('Prediction Error')
    plt.ylabel('Frequency')
    plt.title(f'Error Distribution: {name}')
    plt.axvline(x=0, color='r', linestyle='--', alpha=0.7)
plt.tight_layout()
#plt.savefig('error_distributions.png', dpi=300)
plt.show()

# 10. Comprehensive Training Metrics Visualization
plt.figure(figsize=(20, 15))

# 1. Model Performance Comparison (Multiple Metrics)
plt.subplot(3, 3, 1)
metrics = ['R2', 'RMSE', 'MAE']
x_pos = np.arange(len(results_df))
width = 0.25

for i, metric in enumerate(metrics):
    plt.bar(x_pos + i*width, results_df[metric], width, label=metric, alpha=0.8)

plt.xlabel('Models')
plt.ylabel('Score')
plt.title('Model Performance Comparison')
plt.xticks(x_pos + width, results_df['Model'], rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# 2. R² Score Ranking
plt.subplot(3, 3, 2)
colors = plt.cm.viridis(np.linspace(0, 1, len(results_df)))
bars = plt.bar(results_df['Model'], results_df['R2'], color=colors)
plt.title('R² Score by Model')
plt.ylabel('R² Score')
plt.xticks(rotation=45)
plt.ylim(0, 1)
for bar, score in zip(bars, results_df['R2']):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{score:.3f}', ha='center', va='bottom', fontsize=9)

# 3. RMSE Comparison
plt.subplot(3, 3, 3)
plt.bar(results_df['Model'], results_df['RMSE'], color='coral', alpha=0.7)
plt.title('RMSE by Model')
plt.ylabel('RMSE')
plt.xticks(rotation=45)
for i, (model, rmse) in enumerate(zip(results_df['Model'], results_df['RMSE'])):
    plt.text(i, rmse + rmse*0.01, f'{rmse:.3f}', ha='center', va='bottom', fontsize=9)

# 4. Training vs Test Performance (if available)
plt.subplot(3, 3, 4)
# This would show training vs test metrics if you had training scores
plt.bar(results_df['Model'], results_df['R2'], alpha=0.7, label='Test R²')
plt.title('Model Performance on Test Set')
plt.ylabel('R² Score')
plt.xticks(rotation=45)
plt.legend()
plt.ylim(0, 1)

# 5. Model Complexity vs Performance
plt.subplot(3, 3, 5)
# Approximate model complexity (you can adjust these values)
complexity_scores = {'Random Forest': 8, 'Gradient Boosting': 7, 'Extra Trees': 8, 
                    'XGBoost': 9, 'LightGBM': 8, 'SVR': 5, 'Linear Regression': 1}
model_complexity = [complexity_scores.get(model, 5) for model in results_df['Model']]
plt.scatter(model_complexity, results_df['R2'], s=100, alpha=0.7, c=results_df['RMSE'], cmap='viridis_r')
plt.xlabel('Model Complexity (Approximate)')
plt.ylabel('R² Score')
plt.title('Complexity vs Performance')
plt.colorbar(label='RMSE')
for i, model in enumerate(results_df['Model']):
    plt.annotate(model, (model_complexity[i], results_df['R2'].iloc[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=8)

# 6. Error Analysis Summary
plt.subplot(3, 3, 6)
error_metrics = results_df[['RMSE', 'MAE']]
error_metrics.plot(kind='bar', ax=plt.gca())
plt.title('Error Metrics Comparison')
plt.ylabel('Error Value')
plt.xticks(range(len(results_df)), results_df['Model'], rotation=45)
plt.legend()

# 7. Model Ranking Summary
plt.subplot(3, 3, 7)
# Create a ranking based on R² score
ranking = results_df.sort_values('R2', ascending=False).reset_index(drop=True)
colors = ['gold', 'silver', '#CD7F32'] + ['lightblue'] * (len(ranking) - 3)
plt.barh(range(len(ranking)), ranking['R2'], color=colors[:len(ranking)])
plt.yticks(range(len(ranking)), ranking['Model'])
plt.xlabel('R² Score')
plt.title('Model Ranking (by R² Score)')
plt.xlim(0, 1)
for i, score in enumerate(ranking['R2']):
    plt.text(score + 0.01, i, f'{score:.3f}', va='center', fontsize=9)

# 8. Performance Distribution
plt.subplot(3, 3, 8)
plt.hist(results_df['R2'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
plt.axvline(results_df['R2'].mean(), color='red', linestyle='--', label=f'Mean: {results_df["R2"].mean():.3f}')
plt.xlabel('R² Score')
plt.ylabel('Frequency')
plt.title('R² Score Distribution')
plt.legend()

# 9. Summary Statistics Table
plt.subplot(3, 3, 9)
plt.axis('off')
summary_stats = results_df[['R2', 'RMSE', 'MAE']].describe()
table_data = []
for stat in ['mean', 'std', 'min', 'max']:
    row = [stat.capitalize()] + [f'{summary_stats.loc[stat, col]:.3f}' for col in ['R2', 'RMSE', 'MAE']]
    table_data.append(row)

table = plt.table(cellText=table_data,
                 colLabels=['Statistic', 'R²', 'RMSE', 'MAE'],
                 cellLoc='center',
                 loc='center')
table.auto_set_font_size(False)
table.set_fontsize(10)
table.scale(1, 2)
plt.title('Performance Summary Statistics', pad=20)

plt.tight_layout()
plt.savefig('comprehensive_training_metrics.png', dpi=300, bbox_inches='tight')
plt.show()

# 11. Save Results
results_df.to_csv('model_performance_metrics.csv', index=False)
print("\n=== TRAINING METRICS SUMMARY ===")
print(f"Best performing model: {results_df.loc[results_df['R2'].idxmax(), 'Model']}")
print(f"Best R² Score: {results_df['R2'].max():.4f}")
print(f"Lowest RMSE: {results_df['RMSE'].min():.4f}")
print(f"Average R² across all models: {results_df['R2'].mean():.4f}")
print("\nModel performance saved to 'model_performance_metrics.csv'")
print("Comprehensive visualization saved to 'comprehensive_training_metrics.png'")