import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import (mean_squared_error, mean_absolute_error,
                            r2_score, explained_variance_score,
                            max_error, mean_absolute_percentage_error)
from sklearn.ensemble import (RandomForestRegressor,
                            GradientBoostingRegressor,
                            ExtraTreesRegressor)
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Set style for all plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 1. Data Preparation (using your actual data)
df = pd.read_csv('Samples_Export_scale_10_23aug2025.csv')
print(df)

# df.drop_duplicates(subset=None, inplace=True)
# print(df)

max(df['B2'])

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# data = pd.read_csv("Samples_Export_Scale15.csv")

# data.drop_duplicates(subset=None, inplace=True)

# data.to_csv("./Samples_NoDuplicate.csv", index=False)

data = df

print(data)
print(data.describe())

# sns.histplot(data["AGB"], kde=True, bins=20)
# plt.xlabel("AGB")
# plt.ylabel("Frequency")
# plt.show()

# print("AGB mean: ", data["AGB"].mean(), "standard deviation: ", data["AGB"].std())

# plt.figure(figsize=(18, 12))

# sns.histplot(data['AGB'], kde=True, bins=30)
# plt.title('AGB Distribution')
# plt.xlabel('AGB')
# plt.ylabel('Frequency')
# plt.show()

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(data[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
plt.show()

# for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
#     plt.subplot(2, 3, i+1)
#     sns.boxplot(data[band])
#     plt.title(f'{band} Distribution')
#     plt.xlabel(band)
# plt.show()

# # B2 normalization
# b2_zscore = (data["B2"] - data["B2"].mean()) / data["B2"].std()
# print(b2_zscore)

# b2_outlier = np.where(np.abs(b2_zscore) > 3)
# print(b2_outlier)

# All outliers
outliers = set()
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    z_score = np.abs((data[band] - data[band].mean()) / data[band].std())
    for j in range(len(data[band])):
        if z_score[j] > 3:
            outliers.add(j)

outliers = list(outliers)

outliers.sort()

print(outliers)

print(len(outliers))

data = data.drop(outliers, axis='index')

print(data)
print(len(data))

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(data[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
plt.show()


df = data

# 2. Data Distribution Plots
plt.figure(figsize=(18, 12))

# AGB Distribution
plt.subplot(2, 3, 1)
sns.histplot(df['AGB'], kde=True, bins=30)
plt.title('AGB Distribution')
plt.xlabel('AGB')
plt.ylabel('Frequency')

# Band Distributions
for i, band in enumerate(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']):
    plt.subplot(2, 3, i+1)
    sns.histplot(df[band], kde=True, bins=20)
    plt.title(f'{band} Distribution')
    plt.xlabel(band)
#plt.tight_layout()
#plt.savefig('data_distributions.png', dpi=300)
#plt.close()

# 3. Correlation Analysis
plt.figure(figsize=(10, 8))
corr_matrix = df.corr()
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Feature Correlation Matrix')
plt.tight_layout()
#plt.savefig('correlation_matrix.png', dpi=300)
#plt.close()

# 4. Train-Test Split
X = df[[ 'B2','B3','B4','B8','B11', 'B12']]
y = df['AGB']/max(df['AGB'])
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

# 5. Model Training
models = {
    'Random Forest': RandomForestRegressor(n_estimators=200, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
    'XGBoost': XGBRegressor(n_estimators=200, random_state=42),
    'LightGBM': LGBMRegressor(n_estimators=200, random_state=42),
    'SVR': SVR(kernel='rbf', C=1.0, epsilon=0.1),
    'Linear Regression': LinearRegression()
}

# Model Training and Evaluation with Additional Metrics
results = []
predictions = {}

for name, model in models.items():
    # Train model
    model.fit(X_train, y_train)

    # Make predictions
    y_pred = model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    mean_err = np.mean(y_test - y_pred)
    median_err = np.median(y_test - y_pred)
    std_err = np.std(y_test - y_pred)

    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100,  # Convert to percentage
        'Mean Error': mean_err,
        'Median Error': median_err,
        'Std Error': std_err
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)
print("\nModel Performance Comparison:")
print(results_df.to_string(index=False))

# 5. Model Training
models = {
    'Random Forest': RandomForestRegressor(n_estimators=200, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
    'XGBoost': XGBRegressor(n_estimators=200, random_state=42),
    'LightGBM': LGBMRegressor(n_estimators=200, random_state=42),
    'SVR': SVR(kernel='rbf', C=1.0, epsilon=0.1),
    'Linear Regression': LinearRegression()
}

# Model Training and Evaluation with Additional Metrics
results = []
predictions = {}

for name, model in models.items():
    # Train model
    model.fit(X_train, y_train)

    # Make predictions
    y_pred = model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    mean_err = np.mean(y_test - y_pred)
    median_err = np.median(y_test - y_pred)
    std_err = np.std(y_test - y_pred)

    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100,  # Convert to percentage
        'Mean Error': mean_err,
        'Median Error': median_err,
        'Std Error': std_err
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)
print("\nModel Performance Comparison:")
print(results_df.to_string(index=False))

import pandas as pd
import numpy as np
from sklearn.model_selection import GridSearchCV, train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from sklearn.metrics import (mean_squared_error, mean_absolute_error, r2_score,
                             explained_variance_score, max_error, mean_absolute_percentage_error)

# --- Assume X_train, X_test, y_train, y_test are already created ---
# Example placeholder data
from sklearn.datasets import make_regression
X, y = make_regression(n_samples=1000, n_features=20, noise=25, random_state=42)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# --------------------------------------------------------------------


# 5. Model Training with Hyperparameter Tuning

# Initial model instances
models = {
    'Random Forest': RandomForestRegressor(random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(random_state=42),
    'XGBoost': XGBRegressor(random_state=42),
    'LightGBM': LGBMRegressor(random_state=42),
    'SVR': SVR(),
    'Linear Regression': LinearRegression()
}

# Parameter grids for tuning
param_grids = {
    'Random Forest': {
        'n_estimators': [100, 200],
        'max_depth': [10, 20],
        'min_samples_split': [2, 5]
    },
    'Gradient Boosting': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'max_depth': [3, 5]
    },
    'XGBoost': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'max_depth': [3, 5]
    },
    'LightGBM': {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'num_leaves': [31, 50]
    },
    'SVR': {
        'C': [1, 10],
        'kernel': ['rbf']
    },
    'Linear Regression': {} # No parameters to tune here
}


# Model Training, Tuning, and Evaluation
results = []
predictions = {}
best_params_found = {}

for name, model in models.items():
    print(f"--- Tuning and Training {name} ---")

    # If there's a parameter grid for the model, perform GridSearchCV
    if param_grids[name]:
        # Set up GridSearchCV
        # cv=3 means 3-fold cross-validation.
        # scoring='r2' tells GridSearchCV to optimize for the R-squared score.
        grid_search = GridSearchCV(estimator=model, param_grid=param_grids[name], cv=3,
                                   scoring='r2', n_jobs=-1, verbose=1)
        
        # Train with grid search
        grid_search.fit(X_train, y_train)
        
        # Get the best model found by the grid search
        best_model = grid_search.best_estimator_
        best_params_found[name] = grid_search.best_params_
        print(f"Best parameters for {name}: {grid_search.best_params_}")
    else:
        # If no parameters to tune, just fit the model
        best_model = model
        best_model.fit(X_train, y_train)
        best_params_found[name] = "N/A"

    # Make predictions with the best model
    y_pred = best_model.predict(X_test)
    predictions[name] = y_pred

    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    explained_variance = explained_variance_score(y_test, y_pred)
    max_err = max_error(y_test, y_pred)
    mape = mean_absolute_percentage_error(y_test, y_pred)
    
    # Store results
    results.append({
        'Model': name,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'Explained Variance': explained_variance,
        'Max Error': max_err,
        'MAPE (%)': mape * 100
    })

# Create and sort results dataframe
results_df = pd.DataFrame(results).sort_values('R2', ascending=False)

# Format the output for better readability
pd.set_option('display.float_format', lambda x: '%.3f' % x)

print("\n--- Best Parameters Found ---")
for name, params in best_params_found.items():
    print(f"{name}: {params}")

print("\n--- Model Performance Comparison (After Tuning) ---")
print(results_df.to_string(index=False))

# 6. Model Performance Plots
plt.figure(figsize=(15, 10))

# Model Comparison Bar Plot
plt.subplot(2, 2, 1)
sns.barplot(x='Model', y='R2', data=results_df)
plt.title('Model Comparison (R² Score)')
plt.xticks(rotation=45)
plt.ylim(0, 1)

plt.subplot(2, 2, 2)
sns.barplot(x='Model', y='RMSE', data=results_df)
plt.title('Model Comparison (RMSE)')
plt.xticks(rotation=45)

plt.subplot(2, 2, 3)
sns.barplot(x='Model', y='MAE', data=results_df)
plt.title('Model Comparison (MAE)')
plt.xticks(rotation=45)

# Actual vs Predicted Scatter Plots
plt.subplot(2, 2, 4)
for name, y_pred in predictions.items():
    plt.scatter(y_test, y_pred, alpha=0.5, label=name)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--')
plt.xlabel('Actual AGB')
plt.ylabel('Predicted AGB')
plt.title('Actual vs Predicted AGB')
plt.legend()

#plt.tight_layout()
#plt.savefig('model_performance_comparison.png', dpi=300)
#plt.close()

# 7. Residual Analysis Plots
plt.figure(figsize=(15, 10))
for i, (name, y_pred) in enumerate(predictions.items(), 1):
    residuals = y_test - y_pred
    plt.subplot(2, 3, i)
    sns.scatterplot(x=y_pred, y=residuals, alpha=0.5)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(f'Residual Plot: {name}')
#plt.tight_layout()
#plt.savefig('residual_analysis.png', dpi=300)
#plt.close()

# 8. Feature Importance Plots
plt.figure(figsize=(15, 10))
for i, (name, model) in enumerate(models.items(), 1):
    if hasattr(model, 'feature_importances_'):
        plt.subplot(2, 3, i)
        importances = pd.Series(model.feature_importances_, index=X.columns)
        importances.sort_values().plot(kind='barh')
        plt.title(f'Feature Importance: {name}')
    elif hasattr(model, 'coef_'):
        plt.subplot(2, 3, i)
        coef = pd.Series(model.coef_, index=X.columns)
        coef.sort_values().plot(kind='barh')
        plt.title(f'Feature Coefficients: {name}')
#plt.tight_layout()
#plt.savefig('feature_importance.png', dpi=300)
#plt.close()

# 9. Prediction Error Distribution
plt.figure(figsize=(15, 10))
for i, (name, y_pred) in enumerate(predictions.items(), 1):
    plt.subplot(2, 3, i)
    errors = y_test - y_pred
    sns.histplot(errors, kde=True, bins=30)
    plt.xlabel('Prediction Error')
    plt.title(f'Error Distribution: {name}')
#plt.tight_layout()
#plt.savefig('error_distributions.png', dpi=300)
#plt.close()


# 10. Save Results
results_df.to_csv('model_performance_metrics.csv', index=False)